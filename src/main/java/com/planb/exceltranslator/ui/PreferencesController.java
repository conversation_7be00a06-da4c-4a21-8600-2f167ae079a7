package com.planb.exceltranslator.ui;

import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.Consumer;
import java.util.concurrent.CompletableFuture;

/**
 * Controller for the Preferences window
 * Handles interface language and API key settings
 */
public class PreferencesController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(PreferencesController.class);
    
    @FXML private ComboBox<String> interfaceLanguageComboBox;
    @FXML private PasswordField deeplApiKeyField;
    @FXML private PasswordField googleApiKeyField;
    @FXML private Label deeplApiStatusIndicator;
    @FXML private Label googleApiStatusIndicator;
    
    // UI Labels for language binding
    @FXML private Tab interfaceTab;
    @FXML private Tab apiKeysTab;
    @FXML private Label interfaceLanguageLabel;
    @FXML private Label languageNoteLabel;
    @FXML private Label deeplKeyLabel;
    @FXML private Label deeplInfoLabel;
    @FXML private Label googleKeyLabel;
    @FXML private Label googleInfoLabel;
    @FXML private Label securityNoteLabel;
    @FXML private Button saveButton;
    @FXML private Button cancelButton;
    
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    private TranslationApplicationService translationService;
    private Stage preferencesStage;
    private Consumer<String> logCallback;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.info("Initializing Preferences Controller...");
        
        configManager = ConfigurationManager.getInstance();
        languageManager = LanguageManager.getInstance();
        
        setupInterfaceLanguageOptions();
        loadCurrentSettings();
        bindUIElementsToLanguage();
        setupApiKeyValidation();

        logger.info("Preferences Controller initialized successfully");
    }
    
    /**
     * Binds UI elements to the language manager for automatic updates
     */
    private void bindUIElementsToLanguage() {
        try {
            // Bind tab text
            interfaceTab.textProperty().bind(languageManager.getStringProperty("preferences.tab.interface"));
            apiKeysTab.textProperty().bind(languageManager.getStringProperty("preferences.tab.apikeys"));
            
            // Bind interface tab labels
            interfaceLanguageLabel.textProperty().bind(languageManager.getStringProperty("preferences.interface.language"));
            languageNoteLabel.textProperty().bind(languageManager.getStringProperty("preferences.interface.note"));
            
            // Bind API keys tab labels
            deeplKeyLabel.textProperty().bind(languageManager.getStringProperty("preferences.api.deepl.label"));
            deeplInfoLabel.textProperty().bind(languageManager.getStringProperty("preferences.api.deepl.info"));
            googleKeyLabel.textProperty().bind(languageManager.getStringProperty("preferences.api.google.label"));
            googleInfoLabel.textProperty().bind(languageManager.getStringProperty("preferences.api.google.info"));
            securityNoteLabel.textProperty().bind(languageManager.getStringProperty("preferences.api.security.note"));
            
            // Bind buttons
            saveButton.textProperty().bind(languageManager.getStringProperty("button.save"));
            cancelButton.textProperty().bind(languageManager.getStringProperty("button.cancel.preferences"));
            
            // Bind combo box prompts
            interfaceLanguageComboBox.promptTextProperty().bind(languageManager.getStringProperty("preferences.language.select"));
            deeplApiKeyField.promptTextProperty().bind(languageManager.getStringProperty("preferences.api.deepl.placeholder"));
            googleApiKeyField.promptTextProperty().bind(languageManager.getStringProperty("preferences.api.google.placeholder"));
            
            // Add language change listener to update preferences window
            languageManager.addLanguageChangeListener(locale -> {
                javafx.application.Platform.runLater(() -> {
                    updatePreferencesUIForLanguage();
                });
            });
            
            // Initial update
            updatePreferencesUIForLanguage();
            
            logger.info("Preferences UI elements bound to language manager successfully");
        } catch (Exception e) {
            logger.error("Failed to bind preferences UI elements to language manager", e);
        }
    }
    
    /**
     * Updates the preferences UI for the current language
     */
    private void updatePreferencesUIForLanguage() {
        try {
            // Update the stage title if we have access to it
            if (preferencesStage != null) {
                preferencesStage.setTitle(languageManager.getString("preferences.title"));
            }
            
            logger.debug("Preferences UI labels updated for current language");
        } catch (Exception e) {
            logger.error("Failed to update preferences UI labels for language change", e);
        }
    }
    
    /**
     * Sets up the interface language combo box with available options
     */
    private void setupInterfaceLanguageOptions() {
        ObservableList<String> languages = FXCollections.observableArrayList(
            "English (Default)",
            "日本語 (Japanese)", 
            "Tiếng Việt (Vietnamese)"
        );
        
        interfaceLanguageComboBox.setItems(languages);
        interfaceLanguageComboBox.setValue("English (Default)"); // Set default
    }
    
    /**
     * Loads current settings from configuration
     */
    private void loadCurrentSettings() {
        try {
            // Load interface language setting
            String currentLanguage = configManager.getUiLanguage();
            switch (currentLanguage) {
                case "ja":
                    interfaceLanguageComboBox.setValue("日本語 (Japanese)");
                    break;
                case "vi":
                    interfaceLanguageComboBox.setValue("Tiếng Việt (Vietnamese)");
                    break;
                default:
                    interfaceLanguageComboBox.setValue("English (Default)");
                    break;
            }
            
            if (configManager.getDeepLApiKeyFromPreferences().isPresent()) {
                String actualKey = configManager.getDeepLApiKeyFromPreferences().get();
                deeplApiKeyField.setText(actualKey);
                deeplApiKeyField.setPromptText(languageManager.getString("preferences.api.key.set"));
                // Show as valid since it's already saved
                if (deeplApiStatusIndicator != null) {
                    updateApiKeyStatus(deeplApiStatusIndicator, ApiKeyStatus.VALID, "DeepL API key is configured");
                }
            }

            if (configManager.getGoogleApiKeyFromPreferences().isPresent()) {
                String actualKey = configManager.getGoogleApiKeyFromPreferences().get();
                googleApiKeyField.setText(actualKey);
                googleApiKeyField.setPromptText(languageManager.getString("preferences.api.key.set"));
                // Show as valid since it's already saved
                if (googleApiStatusIndicator != null) {
                    updateApiKeyStatus(googleApiStatusIndicator, ApiKeyStatus.VALID, "Google API key is configured");
                }
            }
            
        } catch (Exception e) {
            logger.error("Failed to load current settings", e);
        }
    }
    
    /**
     * Handles the Save button click
     */
    @FXML
    private void handleSave() {
        try {
            logger.info("Saving preferences...");
            logMessage("Saving preferences changes...");
            
            // Save interface language setting
            String selectedLanguage = interfaceLanguageComboBox.getValue();
            String languageCode;
            switch (selectedLanguage) {
                case "日本語 (Japanese)":
                    languageCode = "ja";
                    break;
                case "Tiếng Việt (Vietnamese)":
                    languageCode = "vi";
                    break;
                default:
                    languageCode = "en";
                    break;
            }
            
            // Check if language actually changed
            String currentLanguage = configManager.getUserPreference("interface.language");
            boolean languageChanged = !languageCode.equals(currentLanguage);
            
            if (languageChanged) {
                // Apply language change immediately
                languageManager.setLanguage(languageCode);
                
                // Save language setting to user preferences
                configManager.setUserPreference("interface.language", languageCode);
                
                String languageName = selectedLanguage.contains("(") ? 
                    selectedLanguage.substring(0, selectedLanguage.indexOf("(")).trim() : selectedLanguage;
                logMessage("Interface language changed to: " + languageName);
                logger.info("Interface language changed from '{}' to '{}'", currentLanguage, languageCode);
            }
            
            // Save API keys (only if they were actually changed)
            boolean apiKeysUpdated = false;
            String deeplKey = deeplApiKeyField.getText();
            if (!deeplKey.isEmpty()) {
                configManager.setUserPreference("deepl.api.key", deeplKey);
                logger.info("DeepL API key updated");
                logMessage("DeepL API key updated successfully");
                apiKeysUpdated = true;
            }

            String googleKey = googleApiKeyField.getText();
            if (!googleKey.isEmpty()) {
                configManager.setUserPreference("google.api.key", googleKey);
                logger.info("Google API key updated");
                logMessage("Google Translate API key updated successfully");
                apiKeysUpdated = true;
            }
            
            // Save user preferences to file
            configManager.saveUserPreferences();
            
            // Refresh translation services if API keys were updated
            if (apiKeysUpdated && translationService != null) {
                translationService.refreshTranslationServices();
                logger.info("Translation services refreshed due to API key changes");
                logMessage("Translation services refreshed with new API keys");
            }
            
            // Show success message in the new language
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(languageManager.getString("preferences.saved.title"));
            alert.setHeaderText(null);
            alert.setContentText(languageManager.getString("preferences.saved.message"));
            alert.showAndWait();
            
            // Final success log
            if (languageChanged && apiKeysUpdated) {
                logger.info("Preferences saved successfully - Language: {}, API keys updated", languageCode);
                logMessage("Preferences saved: Language and API keys updated successfully");
            } else if (languageChanged) {
                logger.info("Preferences saved successfully - Language changed to: {}", languageCode);
                logMessage("Preferences saved: Interface language updated");
            } else if (apiKeysUpdated) {
                logger.info("Preferences saved successfully - API keys updated");
                logMessage("Preferences saved: API keys updated successfully");
            } else {
                logger.info("Preferences saved successfully - No changes detected");
                logMessage("Preferences saved: No changes were made");
            }
            
            // Close the preferences window
            closePreferencesWindow();
            
        } catch (Exception e) {
            logger.error("Failed to save preferences", e);
            
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Error");
            alert.setHeaderText("Failed to save preferences");
            alert.setContentText("An error occurred while saving your preferences: " + e.getMessage());
            alert.showAndWait();
        }
    }
    
    /**
     * Handles the Cancel button click
     */
    @FXML
    private void handleCancel() {
        logger.info("Preferences cancelled by user");
        closePreferencesWindow();
    }
    
    /**
     * Sets the preferences stage reference
     */
    public void setPreferencesStage(Stage stage) {
        this.preferencesStage = stage;
    }
    
    /**
     * Sets the translation service reference for refreshing services when API keys change
     */
    public void setTranslationService(TranslationApplicationService translationService) {
        this.translationService = translationService;
    }
    
    /**
     * Sets the log callback for sending messages to the main application log
     */
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    private void logMessage(String message) {
        logger.info(message);
        if (logCallback != null) {
            logCallback.accept(message);
        }
    }
    
    private void closePreferencesWindow() {
        if (preferencesStage != null) {
            preferencesStage.close();
        }
    }
    
    @FXML
    private void handleDeeplKeyFieldFocus() {
        // No need to clear the field since we now show the actual API key
        // Just ensure the field is ready for editing
    }

    @FXML
    private void handleGoogleKeyFieldFocus() {
        // No need to clear the field since we now show the actual API key
        // Just ensure the field is ready for editing
    }

    /**
     * Sets up real-time API key validation
     */
    private void setupApiKeyValidation() {
        // Add text change listeners for real-time validation
        deeplApiKeyField.textProperty().addListener((observable, oldValue, newValue) -> {
            validateApiKeyAsync(newValue, deeplApiStatusIndicator, "DeepL");
        });

        googleApiKeyField.textProperty().addListener((observable, oldValue, newValue) -> {
            validateApiKeyAsync(newValue, googleApiStatusIndicator, "Google");
        });

        // Add focus lost listeners for validation
        deeplApiKeyField.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue) { // Focus lost
                validateApiKeyAsync(deeplApiKeyField.getText(), deeplApiStatusIndicator, "DeepL");
            }
        });

        googleApiKeyField.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue) { // Focus lost
                validateApiKeyAsync(googleApiKeyField.getText(), googleApiStatusIndicator, "Google");
            }
        });

        // Validate current field contents after setup
        Platform.runLater(() -> {
            validateApiKeyAsync(deeplApiKeyField.getText(), deeplApiStatusIndicator, "DeepL");
            validateApiKeyAsync(googleApiKeyField.getText(), googleApiStatusIndicator, "Google");
        });
    }

    /**
     * Enum for API key validation status
     */
    private enum ApiKeyStatus {
        CHECKING, VALID, INVALID
    }

    /**
     * Updates the visual status indicator
     */
    private void updateApiKeyStatus(Label indicator, ApiKeyStatus status, String tooltipText) {
        Platform.runLater(() -> {
            switch (status) {
                case CHECKING:
                    indicator.setText("⟳");
                    indicator.setTextFill(javafx.scene.paint.Color.ORANGE);
                    break;
                case VALID:
                    indicator.setText("✓");
                    indicator.setTextFill(javafx.scene.paint.Color.GREEN);
                    break;
                case INVALID:
                    indicator.setText("✗");
                    indicator.setTextFill(javafx.scene.paint.Color.RED);
                    break;
            }

            // Set tooltip
            Tooltip tooltip = new Tooltip(tooltipText);
            Tooltip.install(indicator, tooltip);
        });
    }

    /**
     * Validates API key asynchronously
     */
    private void validateApiKeyAsync(String apiKey, Label indicator, String serviceName) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            updateApiKeyStatus(indicator, ApiKeyStatus.INVALID, serviceName + " API key is empty");
            return;
        }

        // Show checking status
        updateApiKeyStatus(indicator, ApiKeyStatus.CHECKING, "Validating " + serviceName + " API key...");

        // Perform validation in background thread
        CompletableFuture.supplyAsync(() -> {
            try {
                // Basic format validation
                if (serviceName.equals("DeepL")) {
                    return validateDeepLApiKeyFormat(apiKey);
                } else if (serviceName.equals("Google")) {
                    return validateGoogleApiKeyFormat(apiKey);
                }
                return false;
            } catch (Exception e) {
                logger.error("Error validating {} API key", serviceName, e);
                return false;
            }
        }).thenAccept(isValid -> {
            if (isValid) {
                updateApiKeyStatus(indicator, ApiKeyStatus.VALID, serviceName + " API key format is valid");
            } else {
                updateApiKeyStatus(indicator, ApiKeyStatus.INVALID, serviceName + " API key format is invalid");
            }
        });
    }

    /**
     * Validates DeepL API key format
     */
    private boolean validateDeepLApiKeyFormat(String apiKey) {
        // DeepL API keys typically end with ":fx" for free accounts or have a specific format
        // Basic validation: should be at least 20 characters and contain alphanumeric characters
        return apiKey.length() >= 20 && apiKey.matches("[a-zA-Z0-9\\-:]+");
    }

    /**
     * Validates Google API key format
     */
    private boolean validateGoogleApiKeyFormat(String apiKey) {
        // Google API keys are typically 39 characters long and start with "AIza"
        return apiKey.length() >= 35 && apiKey.startsWith("AIza") && apiKey.matches("[a-zA-Z0-9\\-_]+");
    }
}
