package com.planb.exceltranslator.ui;

import com.planb.exceltranslator.application.TranslationApplicationService;
import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.domain.port.ExcelProcessor;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.input.DragEvent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import javafx.scene.control.TextArea;
import javafx.stage.Modality;
import javafx.util.Duration;

import javafx.stage.FileChooser;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

/**
 * JavaFX Controller for the Dashboard UI
 * Handles all user interactions and coordinates with the application service
 */
public class DashboardController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);
    
    @FXML private MenuItem selectFileMenuItem;
    @FXML private MenuItem exportFileMenuItem;
    @FXML private MenuItem exitButton;
    @FXML private MenuItem preferencesSettings;
    @FXML private MenuItem aboutApp;
    
    @FXML private Menu fileMenu;
    @FXML private Menu editMenu;
    @FXML private Menu helpMenu;
    @FXML private MenuItem exitMenuItem;
    @FXML private MenuItem preferencesMenuItem;
    @FXML private MenuItem aboutMenuItem;

    @FXML private Pane fileDragAndDrop;
    @FXML private Label selectedFile;
    @FXML private Button selectFileButton;
    @FXML private Label fileSize;
    @FXML private Label fileSize1;
    
    @FXML private Label progressStatus;
    @FXML private Label processCompletionStatus;
    @FXML private ProgressBar progressBar;
    
    @FXML private TextArea logArea;
    @FXML private Circle statusIndicator;
    @FXML private Label logsLabel;
    
    @FXML private ComboBox<Language> sourceLanguage;
    @FXML private ComboBox<Language> targetLanguage;
    @FXML private ComboBox<TranslatorType> translator;
    @FXML private Spinner<Integer> batchSize;
    
    @FXML private Label sourceLanguageLabel;
    @FXML private Label targetLanguageLabel;
    @FXML private Label translatorLabel;
    @FXML private Label batchSizeLabel;
    @FXML private Label sheetDetectedLabel;
    
    @FXML private AnchorPane sheetsDetectedList;
    @FXML private ScrollPane sheetsScrollPane;
    
    @FXML private Button translateButton;
    @FXML private Button cancelButton;
    @FXML private Button exportButton;
    
    @FXML private Label status;
    
    private ConfigurationManager configManager;
    private TranslationApplicationService translationService;
    private LanguageManager languageManager;
    
    private File selectedExcelFile;
    private List<ExcelSheet> detectedSheets;
    private TranslationResult lastTranslationResult;
    private Task<TranslationResult> currentTranslationTask;
    private Language autoDetectedLanguage;
    
    private StringProperty translateButtonTextProperty;
    private StringProperty statusTextProperty;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.info("Initializing Dashboard Controller...");
        
        try {
            configManager = ConfigurationManager.getInstance();
            translationService = new TranslationApplicationService(configManager);
            languageManager = LanguageManager.getInstance();
            
            initializeLanguage();
            initializeDynamicProperties();
            initializeComboBoxes();
            initializeDragAndDrop();
            initializeSpinner();
            initializeCallbacks();
            initializeProgressBar();
            initializeLogging();
            updateUIState();
            bindUIElementsToLanguage();
            
            logger.info("Dashboard Controller initialized successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize Dashboard Controller", e);
            showError("error.title.initialization", "error.message.initialization_failed", e.getMessage());
        }
    }
    
    /**
     * Initializes dynamic UI properties that change based on application state
     */
    private void initializeDynamicProperties() {
        translateButtonTextProperty = new SimpleStringProperty();
        updateTranslateButtonText(false);
        
        statusTextProperty = new SimpleStringProperty();
        updateStatusText(false, false, false);
        
        languageManager.addLanguageChangeListener(language -> {
            boolean isCurrentlyTranslating = currentTranslationTask != null && currentTranslationTask.isRunning();
            updateTranslateButtonText(isCurrentlyTranslating);
            
            boolean hasFile = selectedExcelFile != null;
            boolean hasResult = lastTranslationResult != null && lastTranslationResult.isSuccess();
            updateStatusText(hasFile, isCurrentlyTranslating, hasResult);
        });
    }
    
    /**
     * Updates the translate button text based on translation state
     */
    private void updateTranslateButtonText(boolean isTranslating) {
        if (isTranslating) {
            translateButtonTextProperty.set(languageManager.getString("button.cancel"));
        } else {
            translateButtonTextProperty.set(languageManager.getString("button.translate"));
        }
    }
    
    /**
     * Updates the status text based on application state
     */
    private void updateStatusText(boolean hasFile, boolean isTranslating, boolean hasResult) {
        if (isTranslating) {
            statusTextProperty.set(languageManager.getString("status.translating"));
        } else if (hasResult) {
            statusTextProperty.set(languageManager.getString("status.completed"));
        } else if (hasFile) {
            statusTextProperty.set(languageManager.getString("status.file.loaded"));
        } else {
            statusTextProperty.set(languageManager.getString("status.ready"));
        }
    }
    
    /**
     * Initializes combo boxes with available options and change listeners
     */
    private void initializeComboBoxes() {
        sourceLanguage.getItems().addAll(Language.getSourceLanguages());
        sourceLanguage.setValue(Language.AUTO_DETECT);

        sourceLanguage.setCellFactory(listView -> new javafx.scene.control.ListCell<Language>() {
            @Override
            protected void updateItem(Language item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    String displayText = item.getEnglishName();
                    if (item == autoDetectedLanguage && item != Language.AUTO_DETECT) {
                        displayText += " (Auto-Detected)";
                    }
                    setText(displayText);
                }
            }
        });

        sourceLanguage.setButtonCell(new javafx.scene.control.ListCell<Language>() {
            @Override
            protected void updateItem(Language item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    String displayText = item.getEnglishName();
                    if (item == autoDetectedLanguage && item != Language.AUTO_DETECT) {
                        displayText += " (Auto-Detected)";
                    }
                    setText(displayText);
                }
            }
        });

        sourceLanguage.valueProperty().addListener((observable, oldValue, newValue) -> {
            if (oldValue != null && newValue != null && !oldValue.equals(newValue)) {
                if (newValue != autoDetectedLanguage) {
                    autoDetectedLanguage = null;
                }
                addLogMessage("Source language changed: " + oldValue.getEnglishName() + " → " + newValue.getEnglishName());
            }
        });

        targetLanguage.getItems().addAll(Language.getTargetLanguages());
        targetLanguage.setValue(Language.ENGLISH);

        targetLanguage.valueProperty().addListener((observable, oldValue, newValue) -> {
            if (oldValue != null && newValue != null && !oldValue.equals(newValue)) {
                addLogMessage("Target language changed: " + oldValue.getEnglishName() + " → " + newValue.getEnglishName());
            }
        });

        translator.getItems().addAll(TranslatorType.values());
        translator.setValue(TranslatorType.getDefault());

        translator.valueProperty().addListener((observable, oldValue, newValue) -> {
            if (oldValue != null && newValue != null && !oldValue.equals(newValue)) {
                addLogMessage("Translator changed: " + oldValue.getEnglishName() + " → " + newValue.getEnglishName());
            }
        });

        logger.debug("Combo boxes initialized with change listeners");
    }
    
    /**
     * Initializes drag and drop functionality
     */
    private void initializeDragAndDrop() {
        fileDragAndDrop.setOnDragOver(this::handleDragOver);
        fileDragAndDrop.setOnDragDropped(this::handleDragDropped);
        
        logger.debug("Drag and drop initialized");
    }
    
    /**
     * Initializes the batch size spinner with change listener
     */
    private void initializeSpinner() {
        SpinnerValueFactory<Integer> valueFactory = new SpinnerValueFactory.IntegerSpinnerValueFactory(
                1, configManager.getMaxBatchSize(), configManager.getDefaultBatchSize());
        batchSize.setValueFactory(valueFactory);
        batchSize.setEditable(true);

        batchSize.valueProperty().addListener((observable, oldValue, newValue) -> {
            if (oldValue != null && newValue != null && !oldValue.equals(newValue)) {
                addLogMessage("Batch size changed: " + oldValue + " → " + newValue + " cells per request");
            }
        });

        logger.debug("Batch size spinner initialized with change listener");
    }
    
    /**
     * Initializes callbacks for progress and logging
     */
    private void initializeCallbacks() {
        translationService.setProgressCallback(progress -> {
            Platform.runLater(() -> {
                logger.info("🔄 PROGRESS CALLBACK: {}% - {}", progress.getPercent(), progress.getMessage());

                progressBar.setProgress(progress.getPercent() / 100.0);
                progressStatus.setText(progress.getMessage());
                processCompletionStatus.setText(progress.getPercent() + "%");

                logger.info("✅ Progress bar updated: {}%", progress.getPercent());
            });
        });

        translationService.setLogCallback(message -> {
            Platform.runLater(() -> addLogMessage(message));
        });

        logger.debug("Callbacks initialized");
    }

    /**
     * Initializes the progress bar with default values
     */
    private void initializeProgressBar() {
        progressBar.setProgress(0.0);
        progressStatus.setText("Ready");
        processCompletionStatus.setText("0%");

        progressBar.setVisible(true);
        progressStatus.setVisible(true);
        processCompletionStatus.setVisible(true);

        progressBar.setStyle("-fx-accent: #0098D1;");

        logger.info("Progress bar initialized and set to always visible");
    }

    /**
     * Initializes the logging display
     */
    private void initializeLogging() {
        if (logArea == null) {
            logger.error("logArea TextArea is null - FXML injection failed");
            return;
        }

        logArea.clear();

        // TextArea has built-in scrolling, so we don't need external ScrollPane
        logArea.setWrapText(false); // Keep each log message on a single line
        logArea.setEditable(false);

        // Set up keyboard navigation for the TextArea
        logArea.setFocusTraversable(true);
        logArea.setOnKeyPressed(event -> {
            switch (event.getCode()) {
                case HOME:
                    logArea.home();
                    event.consume();
                    break;
                case END:
                    logArea.end();
                    event.consume();
                    break;
                default:
                    // Let TextArea handle other navigation keys naturally
                    break;
            }
        });

        initializeAutoScrollBehavior();

        updateStatusIndicator("ready");

        addLogMessage("========== APPLICATION INITIALIZED ==========");
        addLogMessage("Excel Translator initialized - Ready to process files");
        addLogMessage("Drag and drop an Excel file or click 'Select File' to begin");

        logger.debug("Logging display initialized");
    }

    /**
     * Gets an alternative target language when source and target conflict
     */
    private Language getAlternativeTargetLanguage(Language sourceLanguage) {
        switch (sourceLanguage) {
            case ENGLISH:
                return Language.JAPANESE;
            case JAPANESE:
                return Language.ENGLISH;
            case VIETNAMESE:
                return Language.ENGLISH;
            default:
                return sourceLanguage == Language.ENGLISH ? Language.JAPANESE : Language.ENGLISH;
        }
    }

    /**
     * Updates the status indicator color based on application state
     */
    private void updateStatusIndicator(String status) {
        if (statusIndicator == null) {
            return;
        }

        Color color;
        switch (status.toLowerCase()) {
            case "ready":
                color = Color.web("#009060"); // Green - ready
                break;
            case "processing":
                color = Color.web("#FFA500"); // Orange - processing
                break;
            case "error":
                color = Color.web("#FF4444"); // Red - error
                break;
            case "success":
                color = Color.web("#00AA00"); // Bright green - success
                break;
            default:
                color = Color.web("#009060"); // Default green
                break;
        }

        statusIndicator.setFill(color);
    }

    /**
     * Initializes the language from saved preferences
     */
    private void initializeLanguage() {
        try {
            String savedLanguage = configManager.getUserPreference("interface.language");
            if (savedLanguage == null || savedLanguage.isEmpty()) {
                savedLanguage = "en"; // Default to English
            }
            languageManager.setLanguage(savedLanguage);
            logger.info("Language initialized to: {}", savedLanguage);
        } catch (Exception e) {
            logger.error("Failed to initialize language, defaulting to English", e);
            languageManager.setLanguage("en");
        }
    }

    /**
     * Binds UI elements to the language manager for automatic updates
     */
    private void bindUIElementsToLanguage() {
        try {
            // Bind all static text elements to language properties
            selectedFile.textProperty().bind(languageManager.getStringProperty("file.dragdrop"));
            selectFileButton.textProperty().bind(languageManager.getStringProperty("file.browse"));
            fileSize1.textProperty().bind(languageManager.getStringProperty("file.browse.helper"));
            
            // Bind menu items
            fileMenu.textProperty().bind(languageManager.getStringProperty("menu.file"));
            editMenu.textProperty().bind(languageManager.getStringProperty("menu.edit"));
            helpMenu.textProperty().bind(languageManager.getStringProperty("menu.help"));
            exitMenuItem.textProperty().bind(languageManager.getStringProperty("menu.file.exit"));
            preferencesMenuItem.textProperty().bind(languageManager.getStringProperty("menu.edit.preferences"));
            aboutMenuItem.textProperty().bind(languageManager.getStringProperty("menu.help.about"));
            
            // Bind section labels
            logsLabel.textProperty().bind(languageManager.getStringProperty("logs.title"));
            sourceLanguageLabel.textProperty().bind(languageManager.getStringProperty("language.source"));
            targetLanguageLabel.textProperty().bind(languageManager.getStringProperty("language.target"));
            translatorLabel.textProperty().bind(languageManager.getStringProperty("translator.title"));
            batchSizeLabel.textProperty().bind(languageManager.getStringProperty("batchsize.title"));
            sheetDetectedLabel.textProperty().bind(languageManager.getStringProperty("sheets.detected"));
            
            // Bind buttons
            translateButton.textProperty().bind(translateButtonTextProperty);
            cancelButton.textProperty().bind(languageManager.getStringProperty("button.cancel"));
            exportButton.textProperty().bind(languageManager.getStringProperty("button.export"));
            
            // Bind combo box prompts
            sourceLanguage.promptTextProperty().bind(languageManager.getStringProperty("language.autodetect"));
            targetLanguage.promptTextProperty().bind(languageManager.getStringProperty("language.select.target"));
            translator.promptTextProperty().bind(languageManager.getStringProperty("translator.deepl.recommended"));
            
            // Bind status
            status.textProperty().bind(statusTextProperty);
            
            // Add language change listener to update dynamic content
            languageManager.addLanguageChangeListener(locale -> {
                Platform.runLater(() -> {
                    updateUILabelsForLanguage();
                });
            });
            
            // Initial update
            updateUILabelsForLanguage();
            
            logger.info("UI elements bound to language manager successfully");
        } catch (Exception e) {
            logger.error("Failed to bind UI elements to language manager", e);
        }
    }

    /**
     * Updates UI labels when language changes
     */
    private void updateUILabelsForLanguage() {
        try {
            // Only update dynamic text that cannot be bound directly
            // Most UI elements are now bound in bindUIElementsToLanguage()
            
            logger.debug("UI labels updated for current language");
        } catch (Exception e) {
            logger.error("Failed to update UI labels for language change", e);
        }
    }

    /**
     * Detects and logs the language of the Excel file content
     */
    private void detectAndLogLanguage(List<ExcelSheet> sheets) {
        if (sheets == null || sheets.isEmpty()) {
            return;
        }

        // Run language detection in background to avoid blocking UI
        CompletableFuture.supplyAsync(() -> {
            try {
                // Get some sample text from the first few cells for language detection
                StringBuilder sampleText = new StringBuilder();
                int samplesCollected = 0;
                final int MAX_SAMPLES = 5; // Limit samples for faster detection

                outerLoop:
                for (ExcelSheet sheet : sheets) {
                    for (TranslatableCell cell : sheet.getTranslatableCells()) {
                        if (samplesCollected >= MAX_SAMPLES) {
                            break outerLoop;
                        }

                        String cellText = cell.getOriginalText();
                        if (cellText != null && cellText.trim().length() > 3) {
                            if (sampleText.length() > 0) {
                                sampleText.append(" ");
                            }
                            sampleText.append(cellText.trim());
                            samplesCollected++;
                        }
                    }
                }

                if (sampleText.length() > 0) {
                    // Try to detect language using available translators
                    var translationServiceFactory = new com.planb.exceltranslator.infrastructure.translation.TranslationServiceFactory(configManager);

                    // First try with current translator
                    TranslatorType currentTranslator = translator.getValue();
                    if (currentTranslator != null) {
                        try {
                            var translationService = translationServiceFactory.createTranslationService(currentTranslator);
                            Language detectedLang = translationService.detectLanguage(sampleText.toString());
                            if (detectedLang != null && detectedLang != Language.AUTO_DETECT) {
                                return detectedLang;
                            }
                        } catch (Exception e) {
                            logger.debug("Language detection failed with {}: {}", currentTranslator, e.getMessage());
                        }
                    }

                    // If current translator failed, try the other one
                    for (TranslatorType fallbackTranslator : TranslatorType.values()) {
                        if (fallbackTranslator != currentTranslator) {
                            try {
                                var translationService = translationServiceFactory.createTranslationService(fallbackTranslator);
                                Language detectedLang = translationService.detectLanguage(sampleText.toString());
                                if (detectedLang != null && detectedLang != Language.AUTO_DETECT) {
                                    logger.debug("Language detection succeeded with fallback translator: {}", fallbackTranslator);
                                    return detectedLang;
                                }
                            } catch (Exception e) {
                                logger.debug("Language detection failed with {}: {}", fallbackTranslator, e.getMessage());
                            }
                        }
                    }
                }
                return null;

            } catch (Exception e) {
                logger.debug("Error during language detection: {}", e.getMessage());
                return null;
            }
        }).thenAccept(detectedLanguage -> {
            Platform.runLater(() -> {
                if (detectedLanguage != null && detectedLanguage != Language.AUTO_DETECT) {
                    addLogMessage("Detected language: " + detectedLanguage.getEnglishName());

                    // Automatically set the source language if it's currently on Auto-Detect
                    if (sourceLanguage.getValue() == Language.AUTO_DETECT) {
                        autoDetectedLanguage = detectedLanguage;
                        sourceLanguage.setValue(detectedLanguage);

                        // Check if source and target languages are the same after auto-detection
                        if (detectedLanguage == targetLanguage.getValue()) {
                            // Automatically change target language to avoid conflict
                            Language newTargetLanguage = getAlternativeTargetLanguage(detectedLanguage);
                            targetLanguage.setValue(newTargetLanguage);
                            addLogMessage("Target language automatically changed to " + newTargetLanguage.getEnglishName() + " to avoid conflict with detected source language");
                        }

                        // Refresh the combo box display to show "(Auto-Detected)"
                        sourceLanguage.setButtonCell(new javafx.scene.control.ListCell<Language>() {
                            @Override
                            protected void updateItem(Language item, boolean empty) {
                                super.updateItem(item, empty);
                                if (empty || item == null) {
                                    setText(null);
                                } else {
                                    String displayText = item.getEnglishName();
                                    if (item == autoDetectedLanguage && item != Language.AUTO_DETECT) {
                                        displayText += " (Auto-Detected)";
                                    }
                                    setText(displayText);
                                }
                            }
                        });

                        addLogMessage("Source language automatically set to: " + detectedLanguage.getEnglishName() + " (Auto-Detected)");
                    }
                } else {
                    addLogMessage("Language detection: Unable to determine source language - keeping Auto-Detect");
                }
            });
        });
    }

    /**
     * Calculates and sets optimal batch size based on file content
     */
    private void calculateAndSetOptimalBatchSize(List<ExcelSheet> sheets) {
        if (sheets == null || sheets.isEmpty()) {
            return;
        }

        // Calculate total translatable cells
        int totalCells = sheets.stream()
                .mapToInt(ExcelSheet::getTranslatableCellCount)
                .sum();

        // Calculate optimal batch size based on file size and API limits
        int optimalBatchSize = calculateOptimalBatchSize(totalCells);

        // Only update if the calculated size is different from current
        int currentBatchSize = batchSize.getValue();
        if (optimalBatchSize != currentBatchSize) {
            batchSize.getValueFactory().setValue(optimalBatchSize);
            addLogMessage("Batch size automatically adjusted to " + optimalBatchSize +
                         " cells per request (optimized for " + totalCells + " total cells)");
        } else {
            addLogMessage("Batch size " + currentBatchSize + " is optimal for " + totalCells + " total cells");
        }
    }

    /**
     * Calculates optimal batch size based on total cells and API constraints
     */
    private int calculateOptimalBatchSize(int totalCells) {
        // API rate limits and constraints
        final int MAX_BATCH_SIZE = configManager.getMaxBatchSize(); // 100
        final int MIN_BATCH_SIZE = 1;
        final int DEFAULT_BATCH_SIZE = configManager.getDefaultBatchSize(); // 50

        // Calculate based on file size categories
        if (totalCells <= 50) {
            // Small files: Use smaller batches for better progress feedback
            return Math.max(MIN_BATCH_SIZE, Math.min(10, totalCells));
        } else if (totalCells <= 200) {
            // Medium files: Use moderate batch size
            return Math.min(25, MAX_BATCH_SIZE);
        } else if (totalCells <= 1000) {
            // Large files: Use default batch size
            return Math.min(DEFAULT_BATCH_SIZE, MAX_BATCH_SIZE);
        } else if (totalCells <= 5000) {
            // Very large files: Use larger batches for efficiency
            return Math.min(75, MAX_BATCH_SIZE);
        } else {
            // Huge files: Use maximum batch size for best performance
            return MAX_BATCH_SIZE;
        }
    }

    /**
     * Handles file selection button click
     */
    @FXML
    private void selectFileButtonClick() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Select Excel File");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Excel Files", "*.xlsx", "*.xls"),
                new FileChooser.ExtensionFilter("All Files", "*.*")
        );
        
        Stage stage = (Stage) selectedFile.getScene().getWindow();
        File file = fileChooser.showOpenDialog(stage);
        
        if (file != null) {
            selectFile(file);
        }
    }
    
    /**
     * Handles cancel button click - resets the dashboard to initial state
     */
    @FXML
    private void cancelButtonClick() {
        logger.info("Cancel button clicked - resetting dashboard");
        
        // Cancel any running translation
        if (currentTranslationTask != null && currentTranslationTask.isRunning()) {
            currentTranslationTask.cancel(true);
            addLogMessage("Translation cancelled by user", LogLevel.WARNING);
            logger.info("Translation task cancelled by user");
        }
        
        resetFileSelection();
        
        sourceLanguage.setValue(Language.AUTO_DETECT);
        targetLanguage.setValue(Language.ENGLISH);
        translator.setValue(TranslatorType.DEEPL);
        batchSize.getValueFactory().setValue(configManager.getDefaultBatchSize());
        
        lastTranslationResult = null;
        currentTranslationTask = null;
        autoDetectedLanguage = null;
        
        updateUIState();
        
        addLogMessage("Dashboard reset to initial state");
        logger.info("Dashboard reset completed");
    }
    
    /**
     * Handles translate button click
     */
    @FXML
    private void translateButtonClick() {
        if (currentTranslationTask != null && currentTranslationTask.isRunning()) {
            // Cancel current translation
            currentTranslationTask.cancel();
            updateUIState();
            addLogMessage("Translation cancelled by user");
            return;
        }
        
        if (selectedExcelFile == null || detectedSheets == null || detectedSheets.isEmpty()) {
            showError("error.title.no_file", "error.message.no_file_selected");
            return;
        }
        
        if (targetLanguage.getValue() == null) {
            showError("error.title.invalid_selection", "error.message.select_target_language");
            return;
        }
        
        if (translator.getValue() == null) {
            showError("error.title.invalid_selection", "error.message.select_translator");
            return;
        }
        
        // Get selected sheets
        List<ExcelSheet> selectedSheets = getSelectedSheets();
        if (selectedSheets.isEmpty()) {
            showError("error.title.no_sheets", "error.message.select_sheets");
            return;
        }
        
        TranslationRequest request = TranslationRequest.builder()
                .excelFile(selectedExcelFile)
                .sourceLanguage(sourceLanguage.getValue())
                .targetLanguage(targetLanguage.getValue())
                .translatorType(translator.getValue())
                .batchSize(batchSize.getValue())
                .selectedSheets(selectedSheets)
                .build();

        addLogMessage("Starting translation with settings:");
        addLogMessage("  • Source: " + sourceLanguage.getValue().getEnglishName() +
                     " → Target: " + targetLanguage.getValue().getEnglishName());
        addLogMessage("  • Translator: " + translator.getValue().getEnglishName());
        addLogMessage("  • Batch Size: " + batchSize.getValue() + " cells per request");
        addLogMessage("  • Selected Sheets: " + selectedSheets.size() + " sheets");

        logger.info("Starting translation with request: {}", request);
        updateStatusIndicator("processing");
        startTranslation(request);
    }
    
    /**
     * Handles export button click
     */
    @FXML
    private void exportButtonClick() {
        if (lastTranslationResult == null || !lastTranslationResult.isSuccess()) {
            showError("error.title.no_result", "error.message.no_translation_result");
            return;
        }
        
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Save Translated File");
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Excel Files", "*.xlsx", "*.xls")
        );
        
        // Suggest a filename
        String originalName = selectedExcelFile.getName();
        String extension = originalName.substring(originalName.lastIndexOf('.'));
        String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
        String suggestedName = baseName + "_translated" + extension;
        fileChooser.setInitialFileName(suggestedName);
        
        Stage stage = (Stage) exportButton.getScene().getWindow();
        File outputFile = fileChooser.showSaveDialog(stage);
        
        if (outputFile != null) {
            exportTranslatedFile(outputFile);
        }
    }
    
    /**
     * Handles drag over event
     */
    private void handleDragOver(DragEvent event) {
        if (event.getGestureSource() != fileDragAndDrop && event.getDragboard().hasFiles()) {
            event.acceptTransferModes(TransferMode.COPY_OR_MOVE);
        }
        event.consume();
    }
    
    /**
     * Handles drag dropped event
     */
    private void handleDragDropped(DragEvent event) {
        Dragboard db = event.getDragboard();
        boolean success = false;
        
        if (db.hasFiles()) {
            List<File> files = db.getFiles();
            if (!files.isEmpty()) {
                File file = files.get(0);
                selectFile(file);
                success = true;
            }
        }
        
        event.setDropCompleted(success);
        event.consume();
    }
    
    /**
     * Selects and analyzes an Excel file
     */
    private void selectFile(File file) {
        if (file == null || !file.exists()) {
            addLogMessage("Selected file does not exist", LogLevel.ERROR);
            showError("error.title.file", "error.message.file_not_exist");
            return;
        }

        if (!file.getName().toLowerCase().endsWith(".xlsx") && !file.getName().toLowerCase().endsWith(".xls")) {
            addLogMessage("Invalid file type - only .xlsx and .xls files are supported", LogLevel.ERROR);
            showError("error.title.invalid_file_type", "error.message.invalid_file_type");
            return;
        }
        
        selectedExcelFile = file;
        
        // Unbind the label from language manager to show file name
        selectedFile.textProperty().unbind();
        selectedFile.setText(file.getName());

        autoDetectedLanguage = null;

        addLogMessage("File selected: " + file.getName() + " (" + (file.length() / 1024) + " KB)");
        updateStatusIndicator("processing");

        addLogMessage("========== FILE ANALYSIS STARTED ==========");
        addLogMessage("Analyzing file structure...");
        try {
            ExcelProcessor.ExcelFileInfo fileInfo = translationService.getFileInfo(file);
            fileSize.setText(fileInfo.getFormattedFileSize());
            addLogMessage("File info: " + fileInfo.getSheetCount() + " sheets, " + fileInfo.getTranslatableCells() + " translatable cells");
            
            // Analyze sheets
            CompletableFuture.supplyAsync(() -> {
                try {
                    return translationService.analyzeExcelFile(file);
                } catch (Exception e) {
                    logger.error("Failed to analyze Excel file", e);
                    Platform.runLater(() -> showError("error.title.analysis", "error.message.analysis_failed", e.getMessage()));
                    return null;
                }
            }).thenAccept(sheets -> {
                if (sheets != null) {
                    Platform.runLater(() -> {
                        detectedSheets = sheets;
                        updateSheetsDisplay();
                        updateUIState();
                        addLogMessage("File analyzed: " + sheets.size() + " sheets with translatable content found");
                        addLogMessage("========== FILE ANALYSIS COMPLETED ==========");

                        // Detect language from file content
                        detectAndLogLanguage(sheets);

                        // Calculate and set optimal batch size
                        calculateAndSetOptimalBatchSize(sheets);
                    });
                }
            });
            
        } catch (Exception e) {
            logger.error("Failed to get file info", e);
            showError("error.title.file", "error.message.file_read_failed", e.getMessage());
        }
    }
    
    /**
     * Resets the file selection and restores default UI state
     */
    private void resetFileSelection() {
        selectedExcelFile = null;
        detectedSheets = null;
        autoDetectedLanguage = null;

        // Restore the default drag-and-drop text binding
        selectedFile.textProperty().bind(languageManager.getStringProperty("file.dragdrop"));

        // Clear file size label
        fileSize.setText("");

        // Clear sheets display
        updateSheetsDisplay();

        logger.info("File selection reset");
    }
    
    /**
     * Updates the sheets display
     */
    private void updateSheetsDisplay() {
        sheetsDetectedList.getChildren().clear();

        if (detectedSheets != null && !detectedSheets.isEmpty()) {
            double yPosition = 15.0;
            final double checkBoxHeight = 30.0;
            final double topPadding = 15.0;
            final double bottomPadding = 15.0;

            for (int i = 0; i < detectedSheets.size(); i++) {
                ExcelSheet sheet = detectedSheets.get(i);

                CheckBox checkBox = new CheckBox(sheet.getDisplayString());
                checkBox.setSelected(sheet.isSelected());
                checkBox.setLayoutX(14.0);
                checkBox.setLayoutY(yPosition);

                // Bind checkbox state to sheet selection
                checkBox.selectedProperty().addListener((obs, oldVal, newVal) -> sheet.setSelected(newVal));

                sheetsDetectedList.getChildren().add(checkBox);
                yPosition += checkBoxHeight;
            }

            // Calculate and set the required height for the container
            double requiredHeight = topPadding + (detectedSheets.size() * checkBoxHeight) + bottomPadding;

            // Set minimum height to ensure scrolling works properly
            double minHeight = Math.max(requiredHeight, 128.0); // 128.0 is the original prefHeight
            sheetsDetectedList.setPrefHeight(minHeight);
            sheetsDetectedList.setMinHeight(minHeight);

            // Force layout update to ensure ScrollPane recognizes the new content size
            Platform.runLater(() -> {
                sheetsDetectedList.autosize();
                if (sheetsScrollPane != null) {
                    sheetsScrollPane.requestLayout();
                }
            });

            logger.debug("Updated sheets display: {} sheets, container height: {}", detectedSheets.size(), minHeight);
        } else {
            // Reset to default height when no sheets
            sheetsDetectedList.setPrefHeight(128.0);
            sheetsDetectedList.setMinHeight(128.0);

            // Force layout update
            Platform.runLater(() -> {
                if (sheetsScrollPane != null) {
                    sheetsScrollPane.requestLayout();
                }
            });
        }
    }
    
    /**
     * Gets the list of selected sheets
     */
    private List<ExcelSheet> getSelectedSheets() {
        if (detectedSheets == null) {
            return List.of();
        }
        
        return detectedSheets.stream()
                .filter(ExcelSheet::isSelected)
                .toList();
    }
    
    /**
     * Starts the translation process
     */
    private void startTranslation(TranslationRequest request) {
        addLogMessage("========== TRANSLATION STARTED ==========");
        addLogMessage("Starting translation: " + request.toString());
        logger.info("Translation task starting, updating UI state...");
        
        currentTranslationTask = new Task<TranslationResult>() {
            @Override
            protected TranslationResult call() throws Exception {
                return translationService.translate(request);
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    lastTranslationResult = getValue();
                    handleTranslationComplete(lastTranslationResult);
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    logger.error("Translation task failed", exception);
                    addLogMessage("Translation failed: " + exception.getMessage(), LogLevel.ERROR);
                    updateStatusIndicator("error");
                    showError("error.title.translation_failed", "error.message.translation_failed", exception.getMessage());
                    updateUIState();
                });
            }

            @Override
            protected void cancelled() {
                Platform.runLater(() -> {
                    addLogMessage("Translation cancelled", LogLevel.WARNING);
                    updateUIState();
                });
            }
        };
        
        Thread translationThread = new Thread(currentTranslationTask);
        translationThread.setDaemon(true);
        translationThread.start();

        // Update UI state to show progress bar
        logger.info("Translation thread started, updating UI to show progress bar");
        updateUIState();
    }
    
    /**
     * Handles translation completion
     */
    private void handleTranslationComplete(TranslationResult result) {
        if (result.isSuccess()) {
            addLogMessage("Translation completed successfully: " + result.getSummaryMessage());
            addLogMessage("========== TRANSLATION COMPLETED ==========");
            updateStatusIndicator("success");
            showInfo("info.title.translation_complete", "success.translation_complete");
        } else {
            StringBuilder errorMessage = new StringBuilder("Translation failed:\n");
            result.getErrors().forEach(error -> errorMessage.append("• ").append(error).append("\n"));

            addLogMessage("Translation failed: " + result.getErrors().size() + " errors", LogLevel.ERROR);
            addLogMessage("========== TRANSLATION FAILED ==========");
            updateStatusIndicator("error");
            showError("error.title.translation_failed", "error.message.translation_failed", errorMessage.toString());
        }

        if (result.hasWarnings()) {
            StringBuilder warningMessage = new StringBuilder("Translation completed with warnings:\n");
            result.getWarnings().forEach(warning -> warningMessage.append("• ").append(warning).append("\n"));
            addLogMessage(result.getWarnings().size() + " warnings occurred during translation", LogLevel.WARNING);
            showWarning("warning.title.translation_warnings", "error.message.translation_failed", warningMessage.toString());
        }
        
        updateUIState();
    }
    
    /**
     * Exports the translated file
     */
    private void exportTranslatedFile(File outputFile) {
        Task<Void> exportTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                translationService.exportTranslatedFile(lastTranslationResult, outputFile);
                return null;
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    addLogMessage("File exported successfully: " + outputFile.getName());
                    addLogMessage("========== FILE EXPORT COMPLETED ==========");
                    showInfo("Export Complete", "Translated file saved successfully:\n" + outputFile.getAbsolutePath());
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    logger.error("Export failed", exception);
                    addLogMessage("========== FILE EXPORT FAILED ==========");
                    showError("error.title.export_failed", "error.message.export_failed", exception.getMessage());
                });
            }
        };
        
        Thread exportThread = new Thread(exportTask);
        exportThread.setDaemon(true);
        exportThread.start();

        addLogMessage("========== FILE EXPORT STARTED ==========");
        addLogMessage("Exporting translated file...");
    }
    
    /**
     * Updates the UI state based on current conditions
     */
    private void updateUIState() {
        boolean hasFile = selectedExcelFile != null;
        boolean hasSheets = detectedSheets != null && !detectedSheets.isEmpty();
        boolean isTranslating = currentTranslationTask != null && currentTranslationTask.isRunning();
        boolean hasResult = lastTranslationResult != null && lastTranslationResult.isSuccess();
        
        // Update button states
        translateButton.setDisable(!hasSheets || isTranslating);
        updateTranslateButtonText(isTranslating);
        
        exportButton.setDisable(!hasResult);
        
        updateStatusText(hasFile, isTranslating, hasResult);
        
        if (isTranslating) {
            progressStatus.setText("Starting translation...");
            processCompletionStatus.setText("0%");
            logger.info("✅ Translation started - progress bar already visible");
        } else {
            progressStatus.setText("Ready");
            processCompletionStatus.setText("0%");
            progressBar.setProgress(0.0);
            logger.info("✅ Translation finished - progress bar reset");
        }
    }
    
    /**
     * Log levels for proper categorization
     */
    public enum LogLevel {
        INFO("INFO:", "#FFFFFF"),      // White for general information
        WARNING("WARNING:", "#FFD93D"), // Yellow for warnings
        ERROR("ERROR:", "#FF6B6B");     // Red for errors

        private final String prefix;
        private final String color;

        LogLevel(String prefix, String color) {
            this.prefix = prefix;
            this.color = color;
        }

        public String getPrefix() { return prefix; }
        public String getColor() { return color; }
    }

    /**
     * Adds a log message to the UI with proper formatting and auto-scroll
     */
    private void addLogMessage(String message) {
        addLogMessage(message, LogLevel.INFO);
    }

    /**
     * Adds a log message with specific log level
     */
    private void addLogMessage(String message, LogLevel level) {
        if (message == null || message.trim().isEmpty()) {
            return;
        }

        // Ensure we're on the JavaFX Application Thread
        if (!Platform.isFxApplicationThread()) {
            Platform.runLater(() -> addLogMessage(message, level));
            return;
        }

        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            
            // Format the log message as plain text
            String formattedMessage = String.format("[%s] %s %s", timestamp, level.getPrefix(), message);
            
            // Update status indicator based on log level
            switch (level) {
                case ERROR:
                    updateStatusIndicator("error");
                    break;
                case WARNING:
                    // Keep current status for warnings, don't override success/processing
                    break;
                case INFO:
                    if (message.toLowerCase().contains("success") || message.toLowerCase().contains("completed")) {
                        updateStatusIndicator("success");
                    } else if (message.toLowerCase().contains("processing") || message.toLowerCase().contains("analyzing")) {
                        updateStatusIndicator("processing");
                    }
                    break;
                default:
                    break;
            }

            // Append the formatted message to the TextArea
            String currentText = logArea.getText();
            String newText = currentText + formattedMessage + "\n";

            // Limit the number of lines to prevent memory issues
            final int MAX_LOG_LINES = 500;
            String[] lines = newText.split("\n");
            if (lines.length > MAX_LOG_LINES) {
                int linesToKeep = MAX_LOG_LINES;
                StringBuilder trimmedText = new StringBuilder();
                for (int i = lines.length - linesToKeep; i < lines.length; i++) {
                    trimmedText.append(lines[i]).append("\n");
                }
                newText = trimmedText.toString();
            }

            logArea.setText(newText);

            // Auto-scroll to bottom for new messages
            Platform.runLater(() -> {
                // Give TextArea time to update its layout
                Platform.runLater(() -> {
                    if (!userIsScrollingManually && logArea != null) {
                        logArea.setScrollTop(Double.MAX_VALUE); // Scroll to bottom
                    }
                });
            });

            // Also log to console for debugging
            logger.info("UI Log [{}]: {}", level.getPrefix(), message);

        } catch (Exception e) {
            logger.error("Failed to add log message to UI: {}", message, e);
        }
    }

    // Auto-scroll behavior tracking
    private boolean userIsScrollingManually = false;
    private Timeline autoScrollResetTimer;

    /**
     * Initialize intelligent auto-scroll behavior
     */
    private void initializeAutoScrollBehavior() {
        if (logArea == null) return;

        // For TextArea, we'll track user interaction to determine manual scrolling
        logArea.setOnMouseClicked(e -> {
            userIsScrollingManually = true;
            resetAutoScrollTimer();
        });

        logArea.setOnKeyPressed(e -> {
            // If user presses navigation keys, consider it manual scrolling
            switch (e.getCode()) {
                case UP:
                case DOWN:
                case PAGE_UP:
                case PAGE_DOWN:
                case HOME:
                case END:
                    userIsScrollingManually = true;
                    resetAutoScrollTimer();
                    break;
                default:
                    break;
            }
        });
    }

    /**
     * Reset auto-scroll timer to resume auto-scrolling after user inactivity
     */
    private void resetAutoScrollTimer() {
        if (autoScrollResetTimer != null) {
            autoScrollResetTimer.stop();
        }

        autoScrollResetTimer = new Timeline(new KeyFrame(Duration.seconds(3), e -> {
            userIsScrollingManually = false;
            // Auto-scroll to bottom if new messages were added while user was scrolling
            Platform.runLater(() -> {
                if (!userIsScrollingManually && logArea != null) {
                    logArea.setScrollTop(Double.MAX_VALUE); // Scroll to bottom
                }
            });
        }));
        autoScrollResetTimer.play();
    }

    /**
     * Convenience methods for different log levels
     */
    
    /**
     * Shows a localized error dialog
     */
    private void showError(String titleKey, String messageKey, Object... messageArgs) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(languageManager.getString(titleKey));
        alert.setHeaderText(null);
        
        String message = languageManager.getString(messageKey);
        if (messageArgs.length > 0) {
            message = java.text.MessageFormat.format(message, messageArgs);
        }
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    /**
     * Shows a localized information dialog
     */
    private void showInfo(String titleKey, String messageKey, Object... messageArgs) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(languageManager.getString(titleKey));
        alert.setHeaderText(null);
        
        String message = languageManager.getString(messageKey);
        if (messageArgs.length > 0) {
            message = java.text.MessageFormat.format(message, messageArgs);
        }
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    /**
     * Shows a localized warning dialog
     */
    private void showWarning(String titleKey, String messageKey, Object... messageArgs) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(languageManager.getString(titleKey));
        alert.setHeaderText(null);
        
        String message = languageManager.getString(messageKey);
        if (messageArgs.length > 0) {
            message = java.text.MessageFormat.format(message, messageArgs);
        }
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // Menu Handlers
    
    @FXML
    private void handleExit() {
        logger.info("Exit requested from menu");
        Platform.exit();
    }
    
    @FXML
    private void handlePreferences() {
        try {
            logger.info("Opening preferences window...");
            
            // Load the preferences FXML
            FXMLLoader fxmlLoader = new FXMLLoader(getClass().getResource("/Preferences.fxml"));
            Parent preferencesRoot = fxmlLoader.load();
            
            // Get the controller and set up the stage reference
            PreferencesController preferencesController = fxmlLoader.getController();
            
            // Create a new stage for the preferences window
            Stage preferencesStage = new Stage();
            preferencesStage.setTitle("Preferences - Excel Translator");
            preferencesStage.setScene(new Scene(preferencesRoot));
            preferencesStage.setResizable(false);
            preferencesStage.initModality(Modality.APPLICATION_MODAL);
            
            // Set the stage reference in the controller
            preferencesController.setPreferencesStage(preferencesStage);
            
            // Set the translation service reference for API key updates
            preferencesController.setTranslationService(translationService);
            
            // Set the log callback so preferences changes appear in UI logs
            preferencesController.setLogCallback(this::addLogMessage);
            
            // Get the owner window
            Stage ownerStage = (Stage) progressBar.getScene().getWindow();
            preferencesStage.initOwner(ownerStage);
            
            // Center the preferences window relative to the main window
            preferencesStage.centerOnScreen();
            
            // Show the preferences window
            preferencesStage.showAndWait();
            
            logger.info("Preferences window closed");
            
        } catch (Exception e) {
            logger.error("Failed to open preferences window", e);
            showError("error.title.preferences", "error.message.preferences_failed", e.getMessage());
        }
    }
    
    /**
     * Handles the Help -> About menu item
     */
    @FXML
    private void handleAbout() {
        logger.info("About dialog requested from menu");
        
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("About Excel Translator");
        alert.setHeaderText("Excel Translator - PLAN-B");
        alert.setContentText(
            "Version: 1.0.0\n\n" +
            "A powerful Excel file translation tool supporting:\n" +
            "• DeepL Translator (Recommended)\n" +
            "• Google Translator\n" +
            "• Multiple languages\n" +
            "• Batch processing\n" +
            "• Automatic backups\n\n" +
            "Developed by PLAN-B\n" +
            "© 2025 All rights reserved."
        );
        
        // Set the owner
        Stage ownerStage = (Stage) progressBar.getScene().getWindow();
        alert.initOwner(ownerStage);
        
        alert.showAndWait();
    }
}
