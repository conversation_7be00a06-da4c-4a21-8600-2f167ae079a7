package com.planb.exceltranslator.domain.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Represents an Excel sheet with translatable cells
 */
public class ExcelSheet {
    private final String name;
    private final int index;
    private final List<TranslatableCell> translatableCells;
    private boolean selected;
    private String translatedName; // Translated sheet name
    
    public ExcelSheet(String name, int index) {
        this.name = Objects.requireNonNull(name, "Sheet name cannot be null");
        this.index = index;
        this.translatableCells = new ArrayList<>();
        this.selected = true; // Default to selected
    }
    
    public String getName() {
        return name;
    }
    
    public int getIndex() {
        return index;
    }
    
    public List<TranslatableCell> getTranslatableCells() {
        return new ArrayList<>(translatableCells);
    }
    
    public void addTranslatableCell(TranslatableCell cell) {
        if (cell != null) {
            translatableCells.add(cell);
        }
    }
    
    public void addTranslatableCells(List<TranslatableCell> cells) {
        if (cells != null) {
            translatableCells.addAll(cells);
        }
    }
    
    public int getTranslatableCellCount() {
        return translatableCells.size();
    }
    
    public boolean isSelected() {
        return selected;
    }
    
    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getTranslatedName() {
        return translatedName;
    }

    public void setTranslatedName(String translatedName) {
        this.translatedName = translatedName;
    }

    public String getEffectiveName() {
        return translatedName != null ? translatedName : name;
    }

    public boolean isNameTranslated() {
        return translatedName != null && !translatedName.trim().isEmpty();
    }
    
    public String getDisplayString() {
        return String.format("%s: %d translatable cells detected", name, getTranslatableCellCount());
    }
    
    /**
     * Checks if the sheet has any translatable cells
     */
    public boolean hasTranslatableCells() {
        return !translatableCells.isEmpty();
    }
    
    /**
     * Gets all untranslated cells
     */
    public List<TranslatableCell> getUntranslatedCells() {
        return translatableCells.stream()
                .filter(cell -> !cell.isTranslated())
                .toList();
    }
    
    /**
     * Gets all translated cells
     */
    public List<TranslatableCell> getTranslatedCells() {
        return translatableCells.stream()
                .filter(TranslatableCell::isTranslated)
                .toList();
    }
    
    /**
     * Calculates translation progress as a percentage
     */
    public double getTranslationProgress() {
        if (translatableCells.isEmpty()) {
            return 100.0;
        }
        
        long translatedCount = translatableCells.stream()
                .mapToLong(cell -> cell.isTranslated() ? 1 : 0)
                .sum();
        
        return (double) translatedCount / translatableCells.size() * 100.0;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExcelSheet that = (ExcelSheet) o;
        return index == that.index && Objects.equals(name, that.name);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, index);
    }
    
    @Override
    public String toString() {
        return String.format("ExcelSheet{name='%s', index=%d, translatableCells=%d, selected=%s}", 
                name, index, translatableCells.size(), selected);
    }
}
