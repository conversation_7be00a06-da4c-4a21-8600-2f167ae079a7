package com.planb.exceltranslator.domain.model;

/**
 * Enu    pub    public static List<Language> getTargetLanguages() {static List<Language> getSourceLanguages() {tion representing supported languages for translation
 */
public enum Language {
    AUTO_DETECT("auto", "Auto-Detect", "自動検出", "Tự động phát hiện"),
    ENGLISH("en", "English", "英語", "Tiếng Anh"),
    JAPANESE("ja", "Japanese", "日本語", "Tiếng Nhật"),
    VIETNAMESE("vi", "Vietnamese", "ベトナム語", "Tiếng Việt");
    
    private final String code;
    private final String englishName;
    private final String japaneseName;
    private final String vietnameseName;
    
    Language(String code, String englishName, String japaneseName, String vietnameseName) {
        this.code = code;
        this.englishName = englishName;
        this.japaneseName = japaneseName;
        this.vietnameseName = vietnameseName;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getEnglishName() {
        return englishName;
    }
    
    public String getJapaneseName() {
        return japaneseName;
    }
    
    public String getVietnameseName() {
        return vietnameseName;
    }
    
    public String getLocalizedName(Language uiLanguage) {
        switch (uiLanguage) {
            case JAPANESE:
                return japaneseName;
            case VIETNAMESE:
                return vietnameseName;
            default:
                return englishName;
        }
    }
    
    public static Language fromCode(String code) {
        for (Language language : values()) {
            if (language.code.equalsIgnoreCase(code)) {
                return language;
            }
        }
        throw new IllegalArgumentException("Unknown language code: " + code);
    }
    
    /**
     * Gets all source languages (including auto-detect)
     */
    public static Language[] getSourceLanguages() {
        return values();
    }
    
    /**
     * Gets all target languages (excluding auto-detect)
     */
    public static Language[] getTargetLanguages() {
        return new Language[]{ENGLISH, JAPANESE, VIETNAMESE};
    }
    
    @Override
    public String toString() {
        return englishName;
    }
}
