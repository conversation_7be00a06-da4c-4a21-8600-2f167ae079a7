package com.planb.exceltranslator.infrastructure.config;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Optional;
import java.util.Properties;

/**
 * Manages application configuration including API keys and settings
 * Supports environment variables and configuration files
 */
public class ConfigurationManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationManager.class);
    private static ConfigurationManager instance;
    
    private final Config config;
    private final Properties userPreferences;
    private final String preferencesFilePath;
    
    private ConfigurationManager() {
        this.config = loadConfiguration();
        this.preferencesFilePath = System.getProperty("user.home") + File.separator + ".excel-translator-preferences.properties";
        this.userPreferences = loadUserPreferences();
    }
    
    public static synchronized ConfigurationManager getInstance() {
        if (instance == null) {
            instance = new ConfigurationManager();
        }
        return instance;
    }
    
    private Config loadConfiguration() {
        try {
            // Load default configuration
            Config defaultConfig = ConfigFactory.load("application.conf");
            
            // Override with system properties and environment variables
            Config systemConfig = ConfigFactory.systemProperties();
            Config envConfig = ConfigFactory.systemEnvironment();
            
            // Merge configurations (later sources override earlier ones)
            Config mergedConfig = envConfig
                    .withFallback(systemConfig)
                    .withFallback(defaultConfig);
            
            logger.info("Configuration loaded successfully");
            return mergedConfig;
            
        } catch (Exception e) {
            logger.warn("Failed to load configuration file, using defaults: {}", e.getMessage());
            return ConfigFactory.empty();
        }
    }
    
    public Optional<String> getDeepLApiKey() {
        return getConfigValue("deepl.api.key", "DEEPL_API_KEY");
    }
    
    public Optional<String> getGoogleApiKey() {
        return getConfigValue("google.api.key", "GOOGLE_API_KEY");
    }
    
    public int getDefaultBatchSize() {
        return getConfigInt("translation.batch.size", 50);
    }
    
    public int getMaxBatchSize() {
        return getConfigInt("translation.batch.max.size", 100);
    }
    
    public int getRequestTimeoutSeconds() {
        return getConfigInt("translation.request.timeout.seconds", 30);
    }
    
    public int getRateLimit() {
        return getConfigInt("translation.rate.limit", 5);
    }
    
    public int getMaxFileSizeMB() {
        return getConfigInt("file.max.size.mb", 50);
    }
    
    public String getTempDirectory() {
        return getConfigString("file.temp.directory", System.getProperty("java.io.tmpdir"));
    }
    
    public String getLogLevel() {
        return getConfigString("logging.level", "INFO");
    }
    
    public boolean isDebugMode() {
        return getConfigBoolean("debug.mode", false);
    }
    
    public String getUiLanguage() {
        // Check user preferences first, then fall back to config
        String userPref = getUserPreference("interface.language");
        if (userPref != null) {
            return userPref;
        }
        return getConfigString("ui.language", "en");
    }
    
    private Properties loadUserPreferences() {
        Properties props = new Properties();
        File prefsFile = new File(preferencesFilePath);
        
        if (prefsFile.exists()) {
            try (FileInputStream fis = new FileInputStream(prefsFile)) {
                props.load(fis);
                logger.info("User preferences loaded from: {}", preferencesFilePath);
            } catch (IOException e) {
                logger.warn("Failed to load user preferences: {}", e.getMessage());
            }
        }
        
        return props;
    }
    
    public String getUserPreference(String key) {
        return userPreferences.getProperty(key);
    }
    
    public void setUserPreference(String key, String value) {
        if (value != null) {
            userPreferences.setProperty(key, value);
        } else {
            userPreferences.remove(key);
        }
    }
    
    public void saveUserPreferences() throws IOException {
        File prefsFile = new File(preferencesFilePath);
        
        // Create parent directories if they don't exist
        File parentDir = prefsFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        try (FileOutputStream fos = new FileOutputStream(prefsFile)) {
            userPreferences.store(fos, "Excel Translator User Preferences");
            logger.info("User preferences saved to: {}", preferencesFilePath);
        }
    }
    
    public Optional<String> getDeepLApiKeyFromPreferences() {
        String userPref = getUserPreference("deepl.api.key");
        if (userPref != null && !userPref.trim().isEmpty()) {
            return Optional.of(userPref.trim());
        }
        return getDeepLApiKey();
    }
    
    public Optional<String> getGoogleApiKeyFromPreferences() {
        String userPref = getUserPreference("google.api.key");
        if (userPref != null && !userPref.trim().isEmpty()) {
            return Optional.of(userPref.trim());
        }
        return getGoogleApiKey();
    }
    
    public boolean isCreateBackup() {
        return getConfigBoolean("file.create.backup", true);
    }
    
    private Optional<String> getConfigValue(String configKey, String envKey) {
        // First check environment variable
        String envValue = System.getenv(envKey);
        if (envValue != null && !envValue.trim().isEmpty()) {
            return Optional.of(envValue.trim());
        }
        
        // Then check configuration file
        try {
            if (config.hasPath(configKey)) {
                String configValue = config.getString(configKey);
                if (configValue != null && !configValue.trim().isEmpty()) {
                    return Optional.of(configValue.trim());
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to get config value for key: {}", configKey, e);
        }
        
        return Optional.empty();
    }
    
    private String getConfigString(String key, String defaultValue) {
        try {
            return config.hasPath(key) ? config.getString(key) : defaultValue;
        } catch (Exception e) {
            logger.debug("Failed to get string config for key: {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    private int getConfigInt(String key, int defaultValue) {
        try {
            return config.hasPath(key) ? config.getInt(key) : defaultValue;
        } catch (Exception e) {
            logger.debug("Failed to get int config for key: {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    private boolean getConfigBoolean(String key, boolean defaultValue) {
        try {
            return config.hasPath(key) ? config.getBoolean(key) : defaultValue;
        } catch (Exception e) {
            logger.debug("Failed to get boolean config for key: {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    public void validateConfiguration() throws ConfigurationException {
        StringBuilder errors = new StringBuilder();
        
        // Check API keys
        if (!getDeepLApiKey().isPresent() && !getGoogleApiKey().isPresent()) {
            errors.append("At least one API key (DeepL or Google) must be configured. ");
        }
        
        // Check temp directory
        String tempDir = getTempDirectory();
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists() || !tempDirFile.canWrite()) {
            errors.append("Temporary directory is not accessible: ").append(tempDir).append(". ");
        }
        
        if (errors.length() > 0) {
            throw new ConfigurationException("Configuration validation failed: " + errors.toString());
        }
    }
    
    public void cleanup() {
        logger.info("Configuration manager cleanup completed");
    }
    
    public static class ConfigurationException extends Exception {
        public ConfigurationException(String message) {
            super(message);
        }
        
        public ConfigurationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
