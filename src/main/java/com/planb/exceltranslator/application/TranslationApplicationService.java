package com.planb.exceltranslator.application;

import com.planb.exceltranslator.domain.model.*;
import com.planb.exceltranslator.domain.port.ExcelProcessor;
import com.planb.exceltranslator.domain.port.TranslationException;
import com.planb.exceltranslator.domain.port.TranslationService;
import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.excel.ApachePOIExcelProcessor;
import com.planb.exceltranslator.infrastructure.translation.TranslationServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * Application service that orchestrates the translation process
 * This is the main business logic layer that coordinates between domain and infrastructure
 */
public class TranslationApplicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(TranslationApplicationService.class);
    
    private final ConfigurationManager configManager;
    private final TranslationServiceFactory translationServiceFactory;
    private final ExcelProcessor excelProcessor;
    
    // Progress tracking
    private Consumer<TranslationProgress> progressCallback;
    private Consumer<String> logCallback;
    
    public TranslationApplicationService(ConfigurationManager configManager) {
        this.configManager = configManager;
        this.translationServiceFactory = new TranslationServiceFactory(configManager);
        this.excelProcessor = new ApachePOIExcelProcessor();
        
        logger.info("Translation Application Service initialized");
    }
    
    public void setProgressCallback(Consumer<TranslationProgress> progressCallback) {
        this.progressCallback = progressCallback;
    }
    
    public void setLogCallback(Consumer<String> logCallback) {
        this.logCallback = logCallback;
    }
    
    public void refreshTranslationServices() {
        logger.info("Refreshing translation services due to configuration changes");
        translationServiceFactory.refreshServices();
        logMessage("Translation services refreshed with updated configuration");
    }
    
    /**
     * Reads an Excel file and returns sheets with translatable content
     */
    public List<ExcelSheet> analyzeExcelFile(File excelFile) throws IOException {
        logMessage("Analyzing Excel file: " + excelFile.getName());
        
        if (!excelProcessor.isSupportedExcelFile(excelFile)) {
            throw new UnsupportedOperationException("Unsupported file format. Please select a .xlsx or .xls file.");
        }
        
        // Check file size
        long fileSizeBytes = excelFile.length();
        long maxSizeBytes = configManager.getMaxFileSizeMB() * 1024 * 1024;
        
        if (fileSizeBytes > maxSizeBytes) {
            throw new IllegalArgumentException(
                String.format("File size (%.1f MB) exceeds maximum allowed size (%d MB)", 
                    fileSizeBytes / (1024.0 * 1024.0), configManager.getMaxFileSizeMB()));
        }
        
        List<ExcelSheet> sheets = excelProcessor.readExcelFile(excelFile);
        
        logMessage(String.format("Analysis complete: Found %d sheets with translatable content", sheets.size()));
        
        return sheets;
    }
    
    /**
     * Gets file information for display purposes
     */
    public ExcelProcessor.ExcelFileInfo getFileInfo(File excelFile) throws IOException {
        return excelProcessor.getFileInfo(excelFile);
    }
    
    /**
     * Performs the translation process asynchronously
     */
    public CompletableFuture<TranslationResult> translateAsync(TranslationRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return translate(request);
            } catch (Exception e) {
                logger.error("Translation failed", e);
                return TranslationResult.builder(request)
                        .success(false)
                        .addError("Translation failed: " + e.getMessage())
                        .build();
            }
        });
    }
    
    /**
     * Performs the translation process synchronously
     */
    public TranslationResult translate(TranslationRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        TranslationResult.Builder resultBuilder = TranslationResult.builder(request)
                .startTime(startTime);
        
        logMessage("Starting translation process...");
        updateProgress(0, "Initializing translation...");
        
        try {
            // Get translation service
            TranslationService translationService = translationServiceFactory
                    .createTranslationService(request.getTranslatorType());
            
            if (!translationService.isAvailable()) {
                throw new TranslationException("Translation service is not available", 
                        TranslationException.ErrorType.SERVICE_UNAVAILABLE);
            }
            
            // Validate language pair
            if (!translationService.isLanguagePairSupported(request.getSourceLanguage(), request.getTargetLanguage())) {
                throw new TranslationException("Language pair not supported", 
                        TranslationException.ErrorType.UNSUPPORTED_LANGUAGE);
            }
            
            // Create backup if configured
            if (configManager.isCreateBackup()) {
                try {
                    File backup = excelProcessor.createBackup(request.getExcelFile());
                    logMessage("Created backup: " + backup.getName());
                } catch (IOException e) {
                    resultBuilder.addWarning("Failed to create backup: " + e.getMessage());
                }
            }
            
            // Process each selected sheet
            List<ExcelSheet> translatedSheets = new ArrayList<>();
            AtomicInteger totalProcessed = new AtomicInteger(0);
            int totalCells = request.getTotalTranslatableCells();
            
            for (ExcelSheet sheet : request.getSelectedSheets()) {
                if (!sheet.isSelected()) {
                    continue;
                }
                
                logMessage("Translating sheet: " + sheet.getName());
                
                try {
                    ExcelSheet translatedSheet = translateSheet(sheet, request, translationService, 
                            totalProcessed, totalCells);
                    translatedSheets.add(translatedSheet);
                    
                } catch (Exception e) {
                    String error = "Failed to translate sheet '" + sheet.getName() + "': " + e.getMessage();
                    resultBuilder.addError(error);
                    logMessage("ERROR: " + error);
                }
            }
            
            // Create statistics
            TranslationResult.TranslationStatistics statistics = createStatistics(translatedSheets, request);
            
            resultBuilder
                    .success(!translatedSheets.isEmpty())
                    .addTranslatedSheets(translatedSheets)
                    .statistics(statistics);
            
            if (translatedSheets.isEmpty()) {
                resultBuilder.addError("No sheets were successfully translated");
            } else {
                logMessage("Translation completed successfully");
                updateProgress(100, "Translation completed");
            }
            
        } catch (Exception e) {
            logger.error("Translation process failed", e);
            String errorMessage = e instanceof TranslationException ? 
                    ((TranslationException) e).getUserFriendlyMessage() : e.getMessage();
            resultBuilder.addError(errorMessage);
            logMessage("ERROR: " + errorMessage);
        }
        
        return resultBuilder.build();
    }
    
    /**
     * Exports the translated content to a new Excel file
     */
    public void exportTranslatedFile(TranslationResult result, File outputFile) throws IOException {
        if (!result.isSuccess() || result.getTranslatedSheets().isEmpty()) {
            throw new IllegalArgumentException("No translated content to export");
        }
        
        logMessage("Exporting translated file: " + outputFile.getName());
        
        excelProcessor.writeTranslatedExcel(
                result.getRequest().getExcelFile(),
                result.getTranslatedSheets(),
                outputFile
        );
        
        logMessage("Export completed: " + outputFile.getName());
    }
    
    /**
     * Checks the availability of translation services
     */
    public java.util.Map<TranslatorType, Boolean> checkServiceAvailability() {
        return translationServiceFactory.checkServiceAvailability();
    }
    
    /**
     * Translates a single sheet
     */
    private ExcelSheet translateSheet(ExcelSheet sheet, TranslationRequest request,
                                    TranslationService translationService,
                                    AtomicInteger totalProcessed, int totalCells)
            throws TranslationException {

        // First, translate the sheet name if it contains translatable text
        if (TranslatableCell.isTranslatable(sheet.getName())) {
            try {
                List<String> translatedNames = translationService.translateBatch(
                        List.of(sheet.getName()),
                        request.getSourceLanguage(),
                        request.getTargetLanguage()
                );
                if (!translatedNames.isEmpty()) {
                    sheet.setTranslatedName(translatedNames.get(0));
                    logMessage("Translated sheet name: '" + sheet.getName() + "' → '" + translatedNames.get(0) + "'");
                }
            } catch (Exception e) {
                logger.warn("Failed to translate sheet name '{}': {}", sheet.getName(), e.getMessage());
                logMessage("Warning: Could not translate sheet name '" + sheet.getName() + "'");
            }
        }

        List<TranslatableCell> cells = sheet.getUntranslatedCells();
        if (cells.isEmpty()) {
            return sheet;
        }
        
        // Process cells in batches
        int batchSize = Math.min(request.getBatchSize(), translationService.getMaxBatchSize());
        
        for (int i = 0; i < cells.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, cells.size());
            List<TranslatableCell> batch = cells.subList(i, endIndex);
            
            // Extract texts for translation
            List<String> textsToTranslate = batch.stream()
                    .map(TranslatableCell::getTextForTranslation)
                    .toList();
            
            try {
                // Translate the batch
                List<String> translatedTexts = translationService.translateBatch(
                        textsToTranslate, 
                        request.getSourceLanguage(), 
                        request.getTargetLanguage()
                );
                
                // Apply translations to cells
                for (int j = 0; j < batch.size() && j < translatedTexts.size(); j++) {
                    TranslatableCell cell = batch.get(j);
                    String translatedText = translatedTexts.get(j);
                    
                    // Apply preservation logic
                    String finalTranslation = cell.applyTranslationWithPreservation(translatedText);
                    cell.setTranslatedText(finalTranslation);
                }
                
                // Update progress
                int processed = totalProcessed.addAndGet(batch.size());
                double progressPercent = (double) processed / totalCells * 100;
                updateProgress((int) progressPercent, 
                        String.format("Translated %d/%d cells", processed, totalCells));
                
                // Rate limiting
                Thread.sleep(1000 / translationService.getRateLimit());
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new TranslationException("Translation interrupted", 
                        TranslationException.ErrorType.GENERAL, "Translation was cancelled");
            } catch (Exception e) {
                throw new TranslationException("Batch translation failed: " + e.getMessage(), e);
            }
        }
        
        return sheet;
    }
    
    /**
     * Creates translation statistics
     */
    private TranslationResult.TranslationStatistics createStatistics(List<ExcelSheet> sheets, 
                                                                    TranslationRequest request) {
        int totalCells = 0;
        int translatedCells = 0;
        int skippedCells = 0;
        int failedCells = 0;
        
        for (ExcelSheet sheet : sheets) {
            for (TranslatableCell cell : sheet.getTranslatableCells()) {
                totalCells++;
                if (cell.isTranslated()) {
                    translatedCells++;
                } else {
                    failedCells++;
                }
            }
        }
        
        int estimatedRequests = request.getEstimatedApiRequests();
        
        return new TranslationResult.TranslationStatistics(
                totalCells, translatedCells, skippedCells, failedCells, estimatedRequests);
    }
    
    private void updateProgress(int percent, String message) {
        if (progressCallback != null) {
            progressCallback.accept(new TranslationProgress(percent, message));
        }
    }
    
    private void logMessage(String message) {
        logger.info(message);
        if (logCallback != null) {
            logCallback.accept(message);
        }
    }
    
    public void cleanup() {
        translationServiceFactory.cleanup();
        logger.info("Translation Application Service cleanup completed");
    }
    
    public static class TranslationProgress {
        private final int percent;
        private final String message;
        
        public TranslationProgress(int percent, String message) {
            this.percent = Math.max(0, Math.min(100, percent));
            this.message = message != null ? message : "";
        }
        
        public int getPercent() { return percent; }
        public String getMessage() { return message; }
        
        @Override
        public String toString() {
            return String.format("TranslationProgress{%d%%, '%s'}", percent, message);
        }
    }
}
