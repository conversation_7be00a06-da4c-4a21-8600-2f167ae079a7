package com.planb.exceltranslator;

import com.planb.exceltranslator.infrastructure.config.ConfigurationManager;
import com.planb.exceltranslator.infrastructure.config.HealthChecker;
import com.planb.exceltranslator.infrastructure.i18n.LanguageManager;
import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Locale;
import java.util.ResourceBundle;

/**
 * Main JavaFX Application class for Excel Translator
 * Supports multi-language UI and performs startup health checks
 */
public class ExcelTranslatorApplication extends Application {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelTranslatorApplication.class);
    private static final String FXML_FILE = "/Dashboard.fxml";
    private static final String APP_TITLE = "Excel Translator - PLAN-B";
    
    private ConfigurationManager configManager;
    private LanguageManager languageManager;
    private HealthChecker healthChecker;
    
    @Override
    public void init() throws Exception {
        super.init();
        
        logger.info("Initializing Excel Translator Application...");
        
        // Initialize configuration manager
        configManager = ConfigurationManager.getInstance();
        
        // Initialize language manager with saved language preference
        languageManager = LanguageManager.getInstance();
        String savedLanguage = configManager.getUserPreference("interface.language");
        if (savedLanguage != null && !savedLanguage.isEmpty()) {
            languageManager.setLanguage(savedLanguage);
        }
        
        // Initialize health checker
        healthChecker = new HealthChecker(configManager);
        
        // Perform startup health checks
        performStartupHealthChecks();
        
        logger.info("Application initialization completed successfully");
    }
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        try {
            logger.info("Starting Excel Translator Application UI...");
            
            // Load resource bundle for internationalization
            ResourceBundle bundle = getResourceBundle();
            
            // Load FXML file
            FXMLLoader loader = new FXMLLoader();
            loader.setLocation(getClass().getResource(FXML_FILE));
            loader.setResources(bundle);
            
            Parent root = loader.load();
            
            // Configure the primary stage
            Scene scene = new Scene(root);

            // Load CSS stylesheet for scrollbar styling
            String cssFile = getClass().getResource("/scrollbar-dark-theme.css").toExternalForm();
            scene.getStylesheets().add(cssFile);

            primaryStage.titleProperty().bind(languageManager.getStringProperty("app.title"));
            primaryStage.setScene(scene);
            primaryStage.setResizable(false);
            primaryStage.setMinWidth(1014);
            primaryStage.setMinHeight(796);
            
            // Set application icon if available
            // primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icon.png")));
            
            // Show the application
            primaryStage.show();
            
            logger.info("Excel Translator Application UI started successfully");
            
        } catch (IOException e) {
            logger.error("Failed to load FXML file: {}", FXML_FILE, e);
            throw new RuntimeException("Failed to start application", e);
        }
    }
    
    @Override
    public void stop() throws Exception {
        logger.info("Shutting down Excel Translator Application...");
        
        // Cleanup resources
        if (configManager != null) {
            configManager.cleanup();
        }
        
        super.stop();
        logger.info("Application shutdown completed");
    }
    
    /**
     * Performs startup health checks to validate configuration and connectivity
     */
    private void performStartupHealthChecks() {
        logger.info("Performing startup health checks...");
        
        try {
            // Check configuration availability
            healthChecker.checkConfiguration();
            logger.info("✓ Configuration check passed");
            
            // Check API credentials
            healthChecker.checkApiCredentials();
            logger.info("✓ API credentials check passed");
            
            // Check internet connectivity
            healthChecker.checkInternetConnectivity();
            logger.info("✓ Internet connectivity check passed");
            
            logger.info("All health checks passed successfully");
            
        } catch (Exception e) {
            logger.warn("Health check failed: {}", e.getMessage());
            // Application can still start with warnings
        }
    }
    
    /**
     * Gets the appropriate resource bundle based on system locale
     * Supports Japanese, Vietnamese, and English
     */
    private ResourceBundle getResourceBundle() {
        Locale systemLocale = Locale.getDefault();
        String language = systemLocale.getLanguage();
        
        // Determine the appropriate locale based on supported languages
        Locale targetLocale;
        switch (language) {
            case "ja":
                targetLocale = Locale.JAPANESE;
                break;
            case "vi":
                targetLocale = new Locale("vi", "VN");
                break;
            default:
                targetLocale = Locale.ENGLISH;
                break;
        }
        
        try {
            return ResourceBundle.getBundle("messages", targetLocale);
        } catch (Exception e) {
            logger.warn("Failed to load resource bundle for locale: {}, falling back to English", targetLocale);
            return ResourceBundle.getBundle("messages", Locale.ENGLISH);
        }
    }
    
    /**
     * Main method to launch the JavaFX application
     */
    public static void main(String[] args) {
        logger.info("Starting Excel Translator Application...");
        launch(args);
    }
}
