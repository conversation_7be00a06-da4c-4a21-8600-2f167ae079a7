/* Reduced width scrollbar styling - 40% reduction from default 15px to 9px */

/* Sheets ScrollPane specific styling */
.sheets-scroll-pane .scroll-bar:vertical {
    -fx-pref-width: 9px;
    -fx-max-width: 9px;
    -fx-min-width: 9px;
}

.sheets-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f0f0f0;
    -fx-background-radius: 4.5px;
    -fx-border-radius: 4.5px;
}

.sheets-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #c0c0c0;
    -fx-background-radius: 4.5px;
    -fx-border-radius: 4.5px;
}

.sheets-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #a0a0a0;
}

.sheets-scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #808080;
}

.sheets-scroll-pane .scroll-bar:vertical .increment-button,
.sheets-scroll-pane .scroll-bar:vertical .decrement-button {
    -fx-pref-width: 9px;
    -fx-max-width: 9px;
    -fx-min-width: 9px;
    -fx-pref-height: 9px;
    -fx-max-height: 9px;
    -fx-min-height: 9px;
}

/* Ensure the viewport background is transparent */
.sheets-scroll-pane .viewport {
    -fx-background-color: transparent;
}

/* General scrollbar styling for consistency */
.sheets-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}
